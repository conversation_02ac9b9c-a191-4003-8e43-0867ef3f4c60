buildscript {
    repositories {
        maven {
            url = 'https://maven.aliyun.com/repository/public'
        }
    }
}

// 根项目配置
allprojects {
    group = 'com.puti.code'
    version = '1.0-SNAPSHOT'

    repositories {
        maven {
            allowInsecureProtocol = true
            url "https://maven.aliyun.com/repository/public/"
        }
    }
}

// 子项目通用配置
subprojects {
    apply plugin: 'java'
    apply plugin: 'java-library'

    java {
        sourceCompatibility = JavaVersion.VERSION_21
        targetCompatibility = JavaVersion.VERSION_21
    }

    compileJava {
        options.encoding = 'UTF-8'
    }

    compileTestJava {
        options.encoding = 'UTF-8'
    }

    test {
        useJUnitPlatform()
    }
}

// 版本管理
ext {
    spoonVersion = '11.2.0'
    nebulaVersion = '3.6.0'
    milvusVersion = '2.5.10'
    openaiVersion = '0.18.2'
    springVersion = '6.2.7'
    lombokVersion = '1.18.30'
    slf4jVersion = '2.0.9'
    logbackVersion = '1.5.13'
    commonsLang3Version = '3.18.0'
    commonsIoVersion = '2.15.1'
    jacksonVersion = '2.16.0'
    junitVersion = '5.10.0'
    mockitoVersion = '5.8.0'
    httpclientVersion = '4.5.13'
    httpcoreVersion = '4.4.13'
}