# 分批处理增强功能说明

## 概述

本次优化主要解决了AI文档生成过程中的两个关键问题：
1. **函数代码内容缺失**：现在包含完整的函数原始代码（MethodInfo.content字段）
2. **上下文大小限制**：通过智能分批处理避免AI模型上下文超限

## 主要改进

### 1. 动态模型配置支持

#### 新增 `ModelConfig` 类
- 支持多种AI模型的配置（GPT-4、Claude、通义千问等）
- 动态获取模型的token限制、字符比例等参数
- 支持自定义模型配置

```java
// 示例：获取模型配置
ModelConfig config = ModelConfig.getConfigByModelName("gpt-4-turbo");
int maxTokens = config.getMaxTokens(); // 128000
int availableChars = config.getAvailableChars(); // 计算可用字符数
```

#### 预定义模型配置
- **GPT系列**: gpt-4, gpt-4-32k, gpt-4-turbo, gpt-3.5-turbo等
- **Claude系列**: claude-3-haiku, claude-3-sonnet, claude-3-opus
- **国产模型**: qwen-turbo, qwen-plus, ernie-bot等

### 2. 完整函数代码包含

#### 移除硬编码过滤
- **之前**: 根据代码长度随意丢弃函数代码
- **现在**: 通过分批处理确保所有函数代码都被包含

```java
// 之前的问题代码（已移除）
if (content.length() < 500) { // 随意丢弃长代码
    prompt.append("```java\n").append(content).append("\n```\n");
}

// 现在的解决方案
// 添加完整函数代码内容
if (method.getContent() != null && !method.getContent().trim().isEmpty()) {
    String content = getDecompressedContent(method.getContent());
    prompt.append("**代码**:\n```java\n").append(content).append("\n```\n\n");
}
```

### 3. 智能分批处理机制

#### 支持所有层级
- **第1层**: 核心流程分批处理
- **第2层**: 详细流程分批处理  
- **第3层**: 完整文档分批处理

#### 动态批次划分
```java
// 基于模型配置动态计算批次大小
ModelConfig modelConfig = ModelConfig.getConfigByModelName(modelName);
int maxCharsPerBatch = BatchProcessingConfig.getMaxCharsForLevel(level, 
    modelConfig.getMaxTokens(), modelConfig.getCharsPerToken(), modelConfig.getReservedTokens());
```

#### 智能内容估算
- 考虑函数代码的实际大小
- 支持压缩内容的解压缩
- 预留格式化字符空间

### 4. 增强的提示词构建

#### 结构化代码展示
```markdown
### 方法名称 [类名]
**描述**: 方法描述信息
**代码**:
```java
// 完整的函数代码
public void methodName() {
    // 实现逻辑
}
```
```

#### 分批处理提示词
- 明确标识批次信息（第X批，共Y批）
- 包含完整的调用关系分析
- 基于代码的深度技术分析要求

## 核心类说明

### 1. `ModelConfig`
- **作用**: 管理不同AI模型的配置参数
- **特性**: 支持预定义配置和自定义配置
- **方法**: `getConfigByModelName()`, `getAvailableChars()`, `calculateTokens()`

### 2. `BatchProcessingConfig`
- **作用**: 管理分批处理的配置参数
- **特性**: 基于模型配置动态计算限制
- **方法**: `getMaxCharsForLevel()`, `exceedsLimit()`, `getSuggestedBatchCount()`

### 3. `BatchDocumentationService`
- **作用**: 处理分批文档生成和合并
- **特性**: 支持并行处理、重试机制
- **方法**: `needsBatchProcessing()`, `generateBatchDocumentation()`

### 4. `AIPromptBuilder` (增强)
- **新增**: 分批提示词构建方法
- **改进**: 完整代码内容包含
- **方法**: `buildLevel1BatchPrompts()`, `buildLevel2BatchPrompts()`, `buildLevel3BatchPrompts()`

## 使用流程

### 1. 自动检测分批需求
```java
if (batchDocumentationService.needsBatchProcessing(context)) {
    // 使用分批处理
    String content = batchDocumentationService.generateBatchDocumentation(context);
} else {
    // 使用单次处理
    String content = generateSingleDocumentation(context);
}
```

### 2. 分批处理流程
1. **内容估算**: 计算所有函数代码的总大小
2. **批次划分**: 根据模型限制智能分批
3. **并行生成**: 多线程并行调用AI服务
4. **结果合并**: 将各批次结果合并为完整文档

### 3. 错误处理
- **重试机制**: 失败批次自动重试
- **部分失败**: 即使部分批次失败也能生成基本文档
- **日志记录**: 详细的处理过程日志

## 配置示例

### 1. 自定义模型配置
```java
ModelConfig customConfig = ModelConfig.builder()
    .modelName("custom-model")
    .maxTokens(64000)
    .charsPerToken(3)
    .reservedTokens(2000)
    .supportsLongContext(true)
    .build();

ModelConfig.addCustomConfig("custom-model", customConfig);
```

### 2. 批处理参数调整
```java
// 在BatchProcessingConfig中调整参数
private static final double LEVEL_1_TOKEN_RATIO = 0.3;  // 第1层使用30%的token
private static final double LEVEL_2_TOKEN_RATIO = 0.6;  // 第2层使用60%的token  
private static final double LEVEL_3_TOKEN_RATIO = 0.9;  // 第3层使用90%的token
```

## 性能优化

### 1. 并行处理
- 使用线程池并行处理多个批次
- 默认最大并发数：3个批次

### 2. 内容压缩
- 自动检测和解压缩函数代码内容
- 支持ContentCompressor的压缩格式

### 3. 智能批次划分
- 按方法层级优先排序
- 避免单个方法跨批次分割
- 动态调整批次大小

## 兼容性

### 1. 向后兼容
- 保持原有API接口不变
- 自动检测是否需要分批处理
- 默认配置确保基本功能正常

### 2. 扩展性
- 支持新增AI模型配置
- 可调整分批处理策略
- 支持自定义提示词模板

## 监控和日志

### 1. 关键指标
- 分批处理触发频率
- 各批次处理时间
- AI服务调用成功率
- 内容大小分布

### 2. 日志级别
- **INFO**: 分批处理决策、批次数量
- **WARN**: 内容过大警告、部分失败
- **ERROR**: 分批处理失败、AI服务异常

## 后续优化建议

1. **动态模型选择**: 根据内容大小自动选择合适的模型
2. **缓存机制**: 缓存相似内容的分析结果
3. **增量更新**: 支持基于代码变更的增量文档更新
4. **质量评估**: 添加生成文档的质量评估机制
