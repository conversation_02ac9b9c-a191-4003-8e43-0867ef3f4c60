package com.puti.code.ai.documentation;

import com.puti.code.base.config.AppConfig;
import com.puti.code.base.model.MethodInfo;
import com.puti.code.base.model.SubgraphData;
import com.puti.code.base.util.ContentCompressor;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.stream.Collectors;

/**
 * AI提示词构建器
 * 根据不同层级和上下文构建优化的提示词，支持函数代码内容和分批处理
 *
 * <AUTHOR>
 */
@Slf4j
public class AIPromptBuilder {

    @Getter
    private final ModelConfig modelConfig;

    public AIPromptBuilder(){
        String openaiModel = AppConfig.getInstance().getChatModel();
        modelConfig = ModelConfig.getConfigByModelName(openaiModel);
    }


    /**
     * 根据层级构建提示词
     *
     * @param context 文档生成上下文
     * @return 构建的提示词
     */
    public String buildPromptForLevel(DocumentationGenerationContext context) {
        try {
            return switch (context.getLevel()) {
                case 1 -> buildLevel1Prompt(context);
                case 2 -> buildLevel2Prompt(context);
                case 3 -> buildLevel3Prompt(context);
                default -> buildLevel3Prompt(context);
            };
        } catch (Exception e) {
            log.error("构建提示词时发生错误", e);
            return null;
        }
    }

    /**
     * 为第1层构建分批提示词
     */
    public List<String> buildLevel1BatchPrompts(DocumentationGenerationContext context) {
        try {
            List<MethodInfo> level1Methods = context.getSubgraph().getMethodsAtLevel(1);
            List<List<MethodInfo>> batches = splitMethodsIntoBatches(level1Methods, context.getLevel());

            List<String> prompts = new ArrayList<>();
            for (int i = 0; i < batches.size(); i++) {
                String prompt = buildLevel1BatchPrompt(context, batches.get(i), i, batches.size());
                prompts.add(prompt);
            }

            return prompts;
        } catch (Exception e) {
            log.error("构建第1层分批提示词时发生错误", e);
            return Collections.emptyList();
        }
    }

    /**
     * 为第2层构建分批提示词
     */
    public List<String> buildLevel2BatchPrompts(DocumentationGenerationContext context) {
        try {
            List<MethodInfo> level2Methods = context.getSubgraph().getMethodsAtLevel(2);
            List<List<MethodInfo>> batches = splitMethodsIntoBatches(level2Methods, context.getLevel());

            List<String> prompts = new ArrayList<>();
            for (int i = 0; i < batches.size(); i++) {
                String prompt = buildLevel2BatchPrompt(context, batches.get(i), i, batches.size());
                prompts.add(prompt);
            }

            return prompts;
        } catch (Exception e) {
            log.error("构建第2层分批提示词时发生错误", e);
            return Collections.emptyList();
        }
    }

    /**
     * 为第3层构建分批提示词
     */
    public List<String> buildLevel3BatchPrompts(DocumentationGenerationContext context) {
        try {
            List<MethodInfo> allMethods = context.getSubgraph().getAllMethods();
            List<List<MethodInfo>> batches = splitMethodsIntoBatches(allMethods, context.getLevel());

            List<String> prompts = new ArrayList<>();
            for (int i = 0; i < batches.size(); i++) {
                String prompt = buildLevel3BatchPrompt(context, batches.get(i), i, batches.size());
                prompts.add(prompt);
            }

            return prompts;
        } catch (Exception e) {
            log.error("构建第3层分批提示词时发生错误", e);
            return Collections.emptyList();
        }
    }

    /**
     * 构建第1层（核心流程）提示词
     */
    private String buildLevel1Prompt(DocumentationGenerationContext context) {
        // 检查是否需要分批处理
        var level1Methods = context.getSubgraph().getMethodsAtLevel(1);
        if (needsBatchProcessing(level1Methods, 1, context)) {
            return buildLevel1BatchPrompt(context, level1Methods, 0, 1);
        }

        return buildLevel1SinglePrompt(context, level1Methods);
    }

    /**
     * 构建第1层单次提示词
     */
    private String buildLevel1SinglePrompt(DocumentationGenerationContext context, List<MethodInfo> level1Methods) {
        StringBuilder prompt = new StringBuilder();

        prompt.append("你是一个资深的Java架构师。请基于以下代码调用图谱和完整函数代码，生成该业务流程的核心说明书。\n\n");

        // 入口方法信息
        var entryPoint = context.getSubgraph().getEntryPoint();
        prompt.append("**入口方法**: ").append(entryPoint.getFullName()).append("\n");
        prompt.append("**方法签名**: ").append(entryPoint.getSignature()).append("\n\n");

        // 核心调用链（包含完整函数代码）
        prompt.append("**核心调用链及代码**:\n");
        for (var method : level1Methods) {
            prompt.append("### ").append(method.getSimpleIdentifier()).append("\n");
            if (method.getDescription() != null) {
                prompt.append("**描述**: ").append(method.getDescription()).append("\n");
            }

            // 添加完整函数代码内容
            if (method.getContent() != null && !method.getContent().trim().isEmpty()) {
                String content = getDecompressedContent(method.getContent());
                prompt.append("**代码**:\n```java\n").append(content).append("\n```\n\n");
            } else {
                prompt.append("**代码**: 无可用代码内容\n\n");
            }
        }

        // 调用关系
        prompt.append("\n**调用关系**:\n");
        var relations = context.getSubgraph().getRelations().stream()
                .filter(r -> isLevel1Relation(r, context))
                .collect(Collectors.toList());

        for (var relation : relations) {
            prompt.append("- ").append(getMethodName(relation.getSourceMethodId(), context))
                    .append(" → ").append(getMethodName(relation.getTargetMethodId(), context))
                    .append(" (").append(relation.getRelationType()).append(")\n");
        }

        prompt.append("\n请生成包含以下内容的说明书：\n");
        prompt.append("1. **业务流程概述** - 简要描述该方法的主要业务功能\n");
        prompt.append("2. **核心方法说明** - 说明主要方法的作用和职责\n");
        prompt.append("3. **主要业务逻辑** - 描述核心的业务处理流程\n");
        prompt.append("4. **关键数据流转** - 说明重要数据的流转过程\n\n");

        prompt.append("**要求**:\n");
        prompt.append("- 重点关注业务逻辑，避免过多技术细节\n");
        prompt.append("- 说明书长度控制在800字以内\n");
        prompt.append("- 使用中文输出，结构清晰\n");
        prompt.append("- 使用Markdown格式\n");

        return prompt.toString();
    }

    /**
     * 构建第2层（详细流程）提示词
     */
    private String buildLevel2Prompt(DocumentationGenerationContext context) {
        // 检查是否需要分批处理
        var level2Methods = context.getSubgraph().getMethodsAtLevel(2);
        if (needsBatchProcessing(level2Methods, 2, context)) {
            return buildLevel2BatchPrompt(context, level2Methods, 0, 1);
        }

        return buildLevel2SinglePrompt(context, level2Methods);
    }

    /**
     * 构建第2层单次提示词
     */
    private String buildLevel2SinglePrompt(DocumentationGenerationContext context, List<MethodInfo> level2Methods) {
        StringBuilder prompt = new StringBuilder();

        prompt.append("你是一个资深的Java架构师。请基于已有的核心说明书和完整函数代码，补充详细的技术实现说明。\n\n");

        // 现有说明书内容
        if (context.getPreviousDocumentation() != null) {
            prompt.append("**现有核心说明书**:\n");
            prompt.append("```\n");
            prompt.append(context.getPreviousDocumentation().getContent());
            prompt.append("\n```\n\n");
        }

        // 扩展的调用链（包含完整函数代码）
        prompt.append("**扩展调用链及代码**:\n");

        for (var method : level2Methods) {
            prompt.append("### ").append(method.getSimpleIdentifier());
            if (method.getClassName() != null) {
                prompt.append(" [").append(method.getClassName()).append("]");
            }
            prompt.append("\n");

            if (method.getDescription() != null) {
                prompt.append("**描述**: ").append(method.getDescription()).append("\n");
            }

            // 添加完整函数代码内容
            if (method.getContent() != null && !method.getContent().trim().isEmpty()) {
                String content = getDecompressedContent(method.getContent());
                prompt.append("**代码**:\n```java\n").append(content).append("\n```\n\n");
            } else {
                prompt.append("**代码**: 无可用代码内容\n\n");
            }
        }

        // 详细调用关系
        prompt.append("\n**详细调用关系**:\n");
        var relations = context.getSubgraph().getRelations().stream()
                .filter(r -> isLevel2Relation(r, context))
                .collect(Collectors.toList());

        for (var relation : relations) {
            prompt.append("- ").append(getMethodName(relation.getSourceMethodId(), context))
                    .append(" → ").append(getMethodName(relation.getTargetMethodId(), context))
                    .append(" (").append(relation.getRelationType()).append(")");
            if (relation.getLineNumber() != null) {
                prompt.append(" [行号: ").append(relation.getLineNumber()).append("]");
            }
            prompt.append("\n");
        }

        prompt.append("\n请在原有说明书基础上补充以下内容：\n");
        prompt.append("1. **详细方法调用关系** - 补充更详细的方法调用说明\n");
        prompt.append("2. **异常处理机制** - 说明异常处理的策略和流程\n");
        prompt.append("3. **数据验证逻辑** - 描述数据校验和处理逻辑\n");
        prompt.append("4. **性能考虑点** - 提及性能相关的设计考虑\n");
        prompt.append("5. **技术实现细节** - 补充重要的技术实现说明\n\n");

        prompt.append("**要求**:\n");
        prompt.append("- 必须基于提供的完整函数代码进行分析\n");
        prompt.append("- 在原有说明书基础上扩展，保持结构清晰\n");
        prompt.append("- 重点补充技术实现细节\n");
        prompt.append("- 使用中文输出，使用Markdown格式\n");

        return prompt.toString();
    }

    /**
     * 构建第2层分批提示词
     */
    private String buildLevel2BatchPrompt(DocumentationGenerationContext context, List<MethodInfo> methods,
                                        int batchIndex, int totalBatches) {
        StringBuilder prompt = new StringBuilder();

        prompt.append("你是一个资深的Java架构师。这是第2层详细流程说明书");
        if (totalBatches > 1) {
            prompt.append("的第").append(batchIndex + 1).append("批（共").append(totalBatches).append("批）");
        }
        prompt.append("。\n\n");

        // 现有说明书内容
        if (context.getPreviousDocumentation() != null) {
            prompt.append("**现有核心说明书**:\n");
            prompt.append("```\n");
            prompt.append(context.getPreviousDocumentation().getContent());
            prompt.append("\n```\n\n");
        }

        // 本批次的扩展方法及代码
        prompt.append("**本批次扩展方法及代码**:\n");
        for (var method : methods) {
            prompt.append("### ").append(method.getSimpleIdentifier());
            if (method.getClassName() != null) {
                prompt.append(" [").append(method.getClassName()).append("]");
            }
            prompt.append("\n");

            if (method.getDescription() != null) {
                prompt.append("**描述**: ").append(method.getDescription()).append("\n");
            }

            // 添加完整函数代码内容
            if (method.getContent() != null && !method.getContent().trim().isEmpty()) {
                String content = getDecompressedContent(method.getContent());
                prompt.append("**代码**:\n```java\n").append(content).append("\n```\n\n");
            } else {
                prompt.append("**代码**: 无可用代码内容\n\n");
            }
        }

        // 调用关系
        addCallRelations(prompt, methods, context);

        // 生成要求
        prompt.append("\n请在原有说明书基础上，基于提供的完整函数代码补充以下内容：\n");
        prompt.append("1. **详细方法调用关系** - 基于代码的详细方法调用说明\n");
        prompt.append("2. **异常处理机制** - 代码中的异常处理策略和流程\n");
        prompt.append("3. **数据验证逻辑** - 代码中的数据校验和处理逻辑\n");
        prompt.append("4. **性能考虑点** - 基于代码分析的性能相关设计考虑\n");
        prompt.append("5. **技术实现细节** - 基于代码的重要技术实现说明\n\n");

        prompt.append("**要求**:\n");
        prompt.append("- 必须基于提供的完整函数代码进行分析\n");
        prompt.append("- 在原有说明书基础上扩展，保持结构清晰\n");
        prompt.append("- 重点补充技术实现细节\n");
        prompt.append("- 使用中文输出，使用Markdown格式\n");
        if (totalBatches > 1) {
            prompt.append("- 这是分批分析的第").append(batchIndex + 1).append("部分\n");
        }

        return prompt.toString();
    }

    /**
     * 构建第3层（完整文档）提示词 - 如果内容过大则返回提示需要分批处理
     */
    private String buildLevel3Prompt(DocumentationGenerationContext context) {
        // 检查是否需要分批处理
        List<MethodInfo> allMethods = context.getSubgraph().getAllMethods();
        int estimatedSize = estimateContentSize(allMethods);
        int maxSize = BatchProcessingConfig.getMaxCharsForLevel(3);

        if (estimatedSize > maxSize) {
            log.warn("第3层内容过大 ({} 字符)，建议使用分批处理", estimatedSize);
            return buildLevel3OverviewPrompt(context);
        }

        return buildLevel3CompletePrompt(context, allMethods);
    }

    /**
     * 构建第3层概览提示词（当内容过大时）
     */
    private String buildLevel3OverviewPrompt(DocumentationGenerationContext context) {
        StringBuilder prompt = new StringBuilder();

        prompt.append("你是一个资深的Java架构师。请基于代码调用图谱，生成技术说明书的总体概览部分。\n\n");

        // 完整调用图信息
        prompt.append("**完整调用图谱概览**:\n");
        prompt.append("- 总节点数: ").append(context.getSubgraph().getTotalNodes()).append("\n");
        prompt.append("- 总边数: ").append(context.getSubgraph().getTotalEdges()).append("\n");
        prompt.append("- 最大层级: ").append(context.getSubgraph().getMaxLevel()).append("\n\n");

        // 入口方法信息
        var entryPoint = context.getSubgraph().getEntryPoint();
        prompt.append("**入口方法**: ").append(entryPoint.getFullName()).append("\n");
        prompt.append("**方法签名**: ").append(entryPoint.getSignature()).append("\n\n");

        // 按层级统计方法数量
        prompt.append("**各层级方法统计**:\n");
        for (int level = 0; level <= context.getSubgraph().getMaxLevel(); level++) {
            var methods = context.getSubgraph().getMethodsAtLevel(level);
            if (!methods.isEmpty()) {
                prompt.append("- 第").append(level).append("层: ").append(methods.size()).append("个方法\n");
            }
        }

        prompt.append("\n请生成包含以下内容的技术说明书概览：\n");
        prompt.append("1. **整体架构概述** - 系统的整体架构和设计思路\n");
        prompt.append("2. **核心业务流程** - 主要的业务处理流程\n");
        prompt.append("3. **技术栈说明** - 使用的主要技术和框架\n");
        prompt.append("4. **模块划分** - 系统的模块结构和职责\n");
        prompt.append("5. **数据流向** - 主要数据的流转过程\n\n");

        prompt.append("**要求**:\n");
        prompt.append("- 这是一个概览性文档，重点关注整体架构\n");
        prompt.append("- 为后续的详细分析提供基础框架\n");
        prompt.append("- 使用中文输出，使用Markdown格式\n");
        prompt.append("- 长度控制在2000字以内\n");

        return prompt.toString();
    }

    /**
     * 构建第3层完整提示词（当内容不大时）
     */
    private String buildLevel3CompletePrompt(DocumentationGenerationContext context, List<MethodInfo> allMethods) {
        StringBuilder prompt = new StringBuilder();

        prompt.append("你是一个资深的Java架构师。请基于完整的代码调用图谱和函数代码，生成全面的技术说明书。\n\n");

        // 完整调用图信息
        prompt.append("**完整调用图谱**:\n");
        prompt.append("- 总节点数: ").append(context.getSubgraph().getTotalNodes()).append("\n");
        prompt.append("- 总边数: ").append(context.getSubgraph().getTotalEdges()).append("\n");
        prompt.append("- 最大层级: ").append(context.getSubgraph().getMaxLevel()).append("\n\n");

        // 按层级列出所有方法及其代码
        for (int level = 0; level <= context.getSubgraph().getMaxLevel(); level++) {
            var methods = context.getSubgraph().getMethodsAtLevel(level);
            if (!methods.isEmpty()) {
                prompt.append("**第").append(level).append("层方法**:\n");
                for (var method : methods) {
                    prompt.append("### ").append(method.getFullName()).append("\n");
                    if (method.getVisibility() != null) {
                        prompt.append("- 可见性: ").append(method.getVisibility()).append("\n");
                    }
                    if (Boolean.TRUE.equals(method.getIsStatic())) {
                        prompt.append("- 静态方法\n");
                    }

                    // 添加函数代码
                    if (method.getContent() != null && !method.getContent().trim().isEmpty()) {
                        String content = getDecompressedContent(method.getContent());
                        prompt.append("```java\n").append(content).append("\n```\n");
                    }
                    prompt.append("\n");
                }
            }
        }

        // 完整调用关系
        prompt.append("**完整调用关系**:\n");
        var relations = context.getSubgraph().getRelations();
        for (var relation : relations) {
            prompt.append("- ").append(getMethodName(relation.getSourceMethodId(), context))
                    .append(" → ").append(getMethodName(relation.getTargetMethodId(), context))
                    .append(" (").append(relation.getRelationType()).append(")");
            if (relation.getDependencyType() != null) {
                prompt.append(" [").append(relation.getDependencyType()).append("]");
            }
            prompt.append("\n");
        }

        prompt.append("\n请生成包含以下内容的完整技术说明书：\n");
        prompt.append("1. **完整业务流程图** - 详细的业务流程描述\n");
        prompt.append("2. **架构设计说明** - 整体架构和设计思路\n");
        prompt.append("3. **所有方法详细说明** - 每个重要方法的功能和实现\n");
        prompt.append("4. **数据结构定义** - 涉及的主要数据结构\n");
        prompt.append("5. **异常处理策略** - 完整的异常处理机制\n");
        prompt.append("6. **性能优化建议** - 性能相关的建议和注意事项\n");
        prompt.append("7. **维护注意事项** - 代码维护和扩展的注意点\n");
        prompt.append("8. **依赖关系分析** - 模块间的依赖关系说明\n\n");

        prompt.append("**要求**:\n");
        prompt.append("- 结构化输出，层次清晰\n");
        prompt.append("- 技术细节完整，适合开发人员深度参考\n");
        prompt.append("- 基于提供的函数代码进行详细分析\n");
        prompt.append("- 说明书长度控制在8000字以内\n");
        prompt.append("- 使用中文输出，使用Markdown格式\n");

        return prompt.toString();
    }

    /**
     * 构建第3层分批提示词
     */
    private String buildLevel3BatchPrompt(DocumentationGenerationContext context, List<MethodInfo> batchMethods,
                                        int batchIndex, int totalBatches) {
        StringBuilder prompt = new StringBuilder();

        prompt.append("你是一个资深的Java架构师。这是第3层技术说明书的第").append(batchIndex + 1)
              .append("批（共").append(totalBatches).append("批）详细分析。\n\n");

        if (batchIndex == 0) {
            // 第一批包含整体概览
            prompt.append("**整体概览**:\n");
            prompt.append("- 总节点数: ").append(context.getSubgraph().getTotalNodes()).append("\n");
            prompt.append("- 总边数: ").append(context.getSubgraph().getTotalEdges()).append("\n");
            prompt.append("- 最大层级: ").append(context.getSubgraph().getMaxLevel()).append("\n\n");

            var entryPoint = context.getSubgraph().getEntryPoint();
            prompt.append("**入口方法**: ").append(entryPoint.getFullName()).append("\n\n");
        }

        prompt.append("**本批次分析的方法**:\n");
        for (var method : batchMethods) {
            prompt.append("### ").append(method.getFullName()).append("\n");
            if (method.getVisibility() != null) {
                prompt.append("- 可见性: ").append(method.getVisibility()).append("\n");
            }
            if (Boolean.TRUE.equals(method.getIsStatic())) {
                prompt.append("- 静态方法\n");
            }
            if (method.getLevel() != null) {
                prompt.append("- 调用层级: ").append(method.getLevel()).append("\n");
            }

            // 添加函数代码
            if (method.getContent() != null && !method.getContent().trim().isEmpty()) {
                String content = getDecompressedContent(method.getContent());
                prompt.append("```java\n").append(content).append("\n```\n");
            }
            prompt.append("\n");
        }

        // 相关的调用关系
        prompt.append("**相关调用关系**:\n");
        Set<String> batchMethodIds = batchMethods.stream()
                .map(MethodInfo::getMethodId)
                .collect(Collectors.toSet());

        var relations = context.getSubgraph().getRelations().stream()
                .filter(r -> batchMethodIds.contains(r.getSourceMethodId()) ||
                           batchMethodIds.contains(r.getTargetMethodId()))
                .collect(Collectors.toList());

        for (var relation : relations) {
            prompt.append("- ").append(getMethodName(relation.getSourceMethodId(), context))
                    .append(" → ").append(getMethodName(relation.getTargetMethodId(), context))
                    .append(" (").append(relation.getRelationType()).append(")\n");
        }

        if (batchIndex == 0) {
            prompt.append("\n请生成本批次方法的详细技术分析，包括：\n");
            prompt.append("1. **整体架构概述** - 系统整体设计思路\n");
            prompt.append("2. **本批次方法详细分析** - 每个方法的功能、实现逻辑和技术细节\n");
            prompt.append("3. **数据流分析** - 方法间的数据传递和处理\n");
            prompt.append("4. **异常处理分析** - 异常处理机制和策略\n");
        } else if (batchIndex == totalBatches - 1) {
            prompt.append("\n请生成本批次方法的详细技术分析，并提供总结，包括：\n");
            prompt.append("1. **本批次方法详细分析** - 每个方法的功能、实现逻辑和技术细节\n");
            prompt.append("2. **性能优化建议** - 基于代码分析的性能优化建议\n");
            prompt.append("3. **维护注意事项** - 代码维护和扩展的注意点\n");
            prompt.append("4. **总体技术总结** - 整个系统的技术特点和设计亮点\n");
        } else {
            prompt.append("\n请生成本批次方法的详细技术分析，包括：\n");
            prompt.append("1. **本批次方法详细分析** - 每个方法的功能、实现逻辑和技术细节\n");
            prompt.append("2. **方法间协作分析** - 方法间的协作关系和数据流转\n");
            prompt.append("3. **设计模式识别** - 识别使用的设计模式和架构模式\n");
        }

        prompt.append("\n**要求**:\n");
        prompt.append("- 基于提供的函数代码进行深入分析\n");
        prompt.append("- 重点关注代码实现细节和技术特点\n");
        prompt.append("- 使用中文输出，使用Markdown格式\n");
        prompt.append("- 这是分批分析的第").append(batchIndex + 1).append("部分\n");

        return prompt.toString();
    }

    /**
     * 判断是否为第1层关系
     */
    private boolean isLevel1Relation(SubgraphData.CallRelation relation, DocumentationGenerationContext context) {
        var sourceMethod = findMethodById(relation.getSourceMethodId(), context);
        var targetMethod = findMethodById(relation.getTargetMethodId(), context);

        return (sourceMethod != null && sourceMethod.getLevel() != null && sourceMethod.getLevel() <= 1) ||
               (targetMethod != null && targetMethod.getLevel() != null && targetMethod.getLevel() <= 1);
    }

    /**
     * 判断是否为第2层关系
     */
    private boolean isLevel2Relation(SubgraphData.CallRelation relation, DocumentationGenerationContext context) {
        var sourceMethod = findMethodById(relation.getSourceMethodId(), context);
        var targetMethod = findMethodById(relation.getTargetMethodId(), context);

        return (sourceMethod != null && sourceMethod.getLevel() != null && sourceMethod.getLevel() <= 2) ||
               (targetMethod != null && targetMethod.getLevel() != null && targetMethod.getLevel() <= 2);
    }

    /**
     * 根据方法ID获取方法名称
     */
    private String getMethodName(String methodId, DocumentationGenerationContext context) {
        var method = findMethodById(methodId, context);
        return method != null ? method.getSimpleIdentifier() : methodId;
    }

    /**
     * 根据ID查找方法信息
     */
    private MethodInfo findMethodById(String methodId, DocumentationGenerationContext context) {
        return context.getSubgraph().getAllMethods().stream()
                .filter(m -> methodId.equals(m.getMethodId()))
                .findFirst()
                .orElse(null);
    }

    /**
     * 估算方法列表的内容大小
     */
    private int estimateContentSize(List<MethodInfo> methods) {
        int totalSize = 0;
        for (MethodInfo method : methods) {
            // 基础信息大小
            totalSize += method.getFullName() != null ? method.getFullName().length() : 0;
            totalSize += method.getSignature() != null ? method.getSignature().length() : 0;
            totalSize += method.getDescription() != null ? method.getDescription().length() : 0;

            // 函数代码内容大小
            if (method.getContent() != null && !method.getContent().trim().isEmpty()) {
                String content = getDecompressedContent(method.getContent());
                totalSize += content.length();
            }

            // 预留格式化字符
            totalSize += 200; // 预留给markdown格式、标题等
        }
        return totalSize;
    }

    /**
     * 将方法列表分批处理
     */
    private List<List<MethodInfo>> splitMethodsIntoBatches(List<MethodInfo> methods, int level) {
        List<List<MethodInfo>> batches = new ArrayList<>();
        int maxSizePerBatch = BatchProcessingConfig.getMaxCharsForLevel(level,
                modelConfig.getMaxTokens(), modelConfig.getCharsPerToken(), modelConfig.getReservedTokens());

        List<MethodInfo> currentBatch = new ArrayList<>();
        int currentBatchSize = 0;

        // 按层级排序，确保重要方法优先处理
        List<MethodInfo> sortedMethods = methods.stream()
                .sorted((m1, m2) -> {
                    int level1 = m1.getLevel() != null ? m1.getLevel() : Integer.MAX_VALUE;
                    int level2 = m2.getLevel() != null ? m2.getLevel() : Integer.MAX_VALUE;
                    return Integer.compare(level1, level2);
                })
                .collect(Collectors.toList());

        for (MethodInfo method : sortedMethods) {
            int methodSize = estimateMethodSize(method);

            // 如果单个方法就超过批次大小，单独成批
            if (methodSize > maxSizePerBatch) {
                if (!currentBatch.isEmpty()) {
                    batches.add(new ArrayList<>(currentBatch));
                    currentBatch.clear();
                    currentBatchSize = 0;
                }
                batches.add(Arrays.asList(method));
                continue;
            }

            // 如果加入当前方法会超过批次大小，开始新批次
            if (currentBatchSize + methodSize > maxSizePerBatch && !currentBatch.isEmpty()) {
                batches.add(new ArrayList<>(currentBatch));
                currentBatch.clear();
                currentBatchSize = 0;
            }

            currentBatch.add(method);
            currentBatchSize += methodSize;
        }

        // 添加最后一批
        if (!currentBatch.isEmpty()) {
            batches.add(currentBatch);
        }

        log.info("将 {} 个方法分为 {} 批处理", methods.size(), batches.size());
        return batches;
    }

    /**
     * 估算单个方法的大小
     */
    private int estimateMethodSize(MethodInfo method) {
        int size = 0;
        size += method.getFullName() != null ? method.getFullName().length() : 0;
        size += method.getSignature() != null ? method.getSignature().length() : 0;
        size += method.getDescription() != null ? method.getDescription().length() : 0;

        if (method.getContent() != null && !method.getContent().trim().isEmpty()) {
            String content = getDecompressedContent(method.getContent());
            size += content.length();
        }

        return size + 200; // 预留格式化字符
    }

    /**
     * 检查是否需要分批处理
     */
    private boolean needsBatchProcessing(List<MethodInfo> methods, int level, DocumentationGenerationContext context) {
        int maxChars = BatchProcessingConfig.getMaxCharsForLevel(level, modelConfig.getMaxTokens(),
                modelConfig.getCharsPerToken(), modelConfig.getReservedTokens());

        // 估算内容大小
        int estimatedSize = estimateContentSize(methods);

        boolean needsBatch = estimatedSize > maxChars;
        if (needsBatch) {
            log.info("第{}层内容过大({} 字符 > {} 字符)，需要分批处理", level, estimatedSize, maxChars);
        }

        return needsBatch;
    }

    /**
     * 构建第1层分批提示词
     */
    private String buildLevel1BatchPrompt(DocumentationGenerationContext context, List<MethodInfo> methods,
                                        int batchIndex, int totalBatches) {
        StringBuilder prompt = new StringBuilder();

        prompt.append("你是一个资深的Java架构师。这是第1层核心流程说明书");
        if (totalBatches > 1) {
            prompt.append("的第").append(batchIndex + 1).append("批（共").append(totalBatches).append("批）");
        }
        prompt.append("。\n\n");

        // 入口方法信息
        var entryPoint = context.getSubgraph().getEntryPoint();
        prompt.append("**入口方法**: ").append(entryPoint.getFullName()).append("\n");
        prompt.append("**方法签名**: ").append(entryPoint.getSignature()).append("\n\n");

        // 本批次的方法及代码
        prompt.append("**本批次核心方法及代码**:\n");
        for (var method : methods) {
            prompt.append("### ").append(method.getSimpleIdentifier()).append("\n");
            if (method.getDescription() != null) {
                prompt.append("**描述**: ").append(method.getDescription()).append("\n");
            }

            // 添加完整函数代码内容
            if (method.getContent() != null && !method.getContent().trim().isEmpty()) {
                String content = getDecompressedContent(method.getContent());
                prompt.append("**代码**:\n```java\n").append(content).append("\n```\n\n");
            } else {
                prompt.append("**代码**: 无可用代码内容\n\n");
            }
        }

        // 调用关系
        addCallRelations(prompt, methods, context);

        // 生成要求
        prompt.append("\n请基于提供的完整函数代码生成说明书，包含以下内容：\n");
        prompt.append("1. **业务流程概述** - 基于代码分析的主要业务功能\n");
        prompt.append("2. **核心方法说明** - 每个方法的作用、参数、返回值和关键逻辑\n");
        prompt.append("3. **主要业务逻辑** - 基于代码的核心业务处理流程\n");
        prompt.append("4. **关键数据流转** - 基于代码分析的数据流转过程\n\n");

        prompt.append("**要求**:\n");
        prompt.append("- 必须基于提供的完整函数代码进行分析\n");
        prompt.append("- 重点关注业务逻辑，结合代码实现细节\n");
        prompt.append("- 使用中文输出，结构清晰\n");
        prompt.append("- 使用Markdown格式\n");
        if (totalBatches > 1) {
            prompt.append("- 这是分批分析的第").append(batchIndex + 1).append("部分\n");
        }

        return prompt.toString();
    }

    /**
     * 添加调用关系信息
     */
    private void addCallRelations(StringBuilder prompt, List<MethodInfo> methods, DocumentationGenerationContext context) {
        Set<String> methodIds = methods.stream()
                .map(MethodInfo::getMethodId)
                .collect(Collectors.toSet());

        var relations = context.getSubgraph().getRelations().stream()
                .filter(r -> methodIds.contains(r.getSourceMethodId()) || methodIds.contains(r.getTargetMethodId()))
                .collect(Collectors.toList());

        if (!relations.isEmpty()) {
            prompt.append("**相关调用关系**:\n");
            for (var relation : relations) {
                prompt.append("- ").append(getMethodName(relation.getSourceMethodId(), context))
                        .append(" → ").append(getMethodName(relation.getTargetMethodId(), context))
                        .append(" (").append(relation.getRelationType()).append(")\n");
            }
            prompt.append("\n");
        }
    }

    /**
     * 获取解压缩后的内容
     */
    private String getDecompressedContent(String content) {
        if (content == null || content.trim().isEmpty()) {
            return "";
        }

        try {
            // 检查是否是压缩内容
            if (ContentCompressor.isCompressed(content)) {
                return ContentCompressor.decompress(content);
            } else {
                return content;
            }
        } catch (Exception e) {
            log.warn("解压缩内容失败，使用原始内容: {}", e.getMessage());
            return content;
        }
    }
}
