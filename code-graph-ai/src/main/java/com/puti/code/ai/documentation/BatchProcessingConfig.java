package com.puti.code.ai.documentation;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;

/**
 * 分批处理配置类
 * 管理AI文档生成的分批处理参数，支持动态配置不同模型的token限制
 *
 * <AUTHOR>
 */
@Slf4j
@Data
public class BatchProcessingConfig {

    // 默认配置（当无法获取模型配置时使用）
    private static final int DEFAULT_MAX_TOKENS = 4000;
    private static final int DEFAULT_CHARS_PER_TOKEN = 4;
    private static final int DEFAULT_RESERVED_TOKENS = 1000;

    // 层级使用比例配置
    private static final double LEVEL_1_TOKEN_RATIO = 0.3;  // 第1层使用30%的token
    private static final double LEVEL_2_TOKEN_RATIO = 0.6;  // 第2层使用60%的token
    private static final double LEVEL_3_TOKEN_RATIO = 0.9;  // 第3层使用90%的token
    
    // 分批处理配置
    private static final int MAX_METHODS_PER_BATCH = 20;   // 每批最大方法数
    private static final int MIN_BATCH_SIZE = 5;           // 最小批次大小
    private static final int MAX_CONCURRENT_BATCHES = 3;   // 最大并发批次数
    
    // 重试配置
    private static final int MAX_RETRY_ATTEMPTS = 3;       // 最大重试次数
    private static final long RETRY_DELAY_MS = 1000;       // 重试延迟毫秒数
    
    /**
     * 获取指定层级的最大token数（基于模型配置动态计算）
     */
    public static int getMaxTokensForLevel(int level, int modelMaxTokens) {
        double ratio = switch (level) {
            case 1 -> LEVEL_1_TOKEN_RATIO;
            case 2 -> LEVEL_2_TOKEN_RATIO;
            case 3 -> LEVEL_3_TOKEN_RATIO;
            default -> LEVEL_3_TOKEN_RATIO;
        };
        return (int) (modelMaxTokens * ratio);
    }

    /**
     * 获取指定层级的最大token数（使用默认配置）
     */
    public static int getMaxTokensForLevel(int level) {
        return getMaxTokensForLevel(level, DEFAULT_MAX_TOKENS);
    }

    /**
     * 获取指定层级的最大字符数
     */
    public static int getMaxCharsForLevel(int level, int modelMaxTokens, int charsPerToken, int reservedTokens) {
        int maxTokens = getMaxTokensForLevel(level, modelMaxTokens);
        return (maxTokens - reservedTokens) * charsPerToken;
    }

    /**
     * 获取指定层级的最大字符数（使用默认配置）
     */
    public static int getMaxCharsForLevel(int level) {
        return getMaxCharsForLevel(level, DEFAULT_MAX_TOKENS, DEFAULT_CHARS_PER_TOKEN, DEFAULT_RESERVED_TOKENS);
    }

    /**
     * 获取每批最大方法数
     */
    public static int getMaxMethodsPerBatch() {
        return MAX_METHODS_PER_BATCH;
    }

    /**
     * 获取最小批次大小
     */
    public static int getMinBatchSize() {
        return MIN_BATCH_SIZE;
    }

    /**
     * 获取最大并发批次数
     */
    public static int getMaxConcurrentBatches() {
        return MAX_CONCURRENT_BATCHES;
    }

    /**
     * 获取最大重试次数
     */
    public static int getMaxRetryAttempts() {
        return MAX_RETRY_ATTEMPTS;
    }

    /**
     * 获取重试延迟毫秒数
     */
    public static long getRetryDelayMs() {
        return RETRY_DELAY_MS;
    }
    
    /**
     * 计算指定内容大小需要的token数
     */
    public static int calculateTokens(int contentSize) {
        return (contentSize / DEFAULT_CHARS_PER_TOKEN) + DEFAULT_RESERVED_TOKENS;
    }
    
    /**
     * 检查内容大小是否超过指定层级的限制
     */
    public static boolean exceedsLimit(int contentSize, int level) {
        int maxChars = getMaxCharsForLevel(level);
        return contentSize > maxChars;
    }
    
    /**
     * 获取建议的批次数量
     */
    public static int getSuggestedBatchCount(int totalContentSize, int level) {
        int maxCharsPerBatch = getMaxCharsForLevel(level);
        return Math.max(1, (int) Math.ceil((double) totalContentSize / maxCharsPerBatch));
    }
    
    /**
     * 验证批处理配置的合理性
     */
    public static boolean validateConfig() {
        try {
            // 检查基本配置
            if (DEFAULT_MAX_TOKENS <= 0) {
                log.error("Token限制配置无效");
                return false;
            }

            if (DEFAULT_CHARS_PER_TOKEN <= 0 || DEFAULT_RESERVED_TOKENS < 0) {
                log.error("字符和预留token配置无效");
                return false;
            }
            
            if (MAX_METHODS_PER_BATCH <= 0 || MIN_BATCH_SIZE <= 0) {
                log.error("批次大小配置无效");
                return false;
            }
            
            if (MAX_CONCURRENT_BATCHES <= 0) {
                log.error("并发批次数配置无效");
                return false;
            }
            
            if (MAX_RETRY_ATTEMPTS <= 0 || RETRY_DELAY_MS < 0) {
                log.error("重试配置无效");
                return false;
            }
            
            // 检查层级间的合理性
            // 由于现在使用比例配置，这个检查不再需要
            
            log.info("批处理配置验证通过");
            return true;
            
        } catch (Exception e) {
            log.error("验证批处理配置时发生错误", e);
            return false;
        }
    }
    
    /**
     * 打印当前配置信息
     */
    public static void printConfig() {
        log.info("=== 批处理配置信息 ===");
        log.info("默认最大token数: {}", DEFAULT_MAX_TOKENS);
        log.info("每token平均字符数: {}", DEFAULT_CHARS_PER_TOKEN);
        log.info("预留token数: {}", DEFAULT_RESERVED_TOKENS);
        log.info("第1层token比例: {}", LEVEL_1_TOKEN_RATIO);
        log.info("第2层token比例: {}", LEVEL_2_TOKEN_RATIO);
        log.info("第3层token比例: {}", LEVEL_3_TOKEN_RATIO);
        log.info("每批最大方法数: {}", MAX_METHODS_PER_BATCH);
        log.info("最小批次大小: {}", MIN_BATCH_SIZE);
        log.info("最大并发批次数: {}", MAX_CONCURRENT_BATCHES);
        log.info("最大重试次数: {}", MAX_RETRY_ATTEMPTS);
        log.info("重试延迟(ms): {}", RETRY_DELAY_MS);
        log.info("=====================");
    }
}
