package com.puti.code.ai.documentation;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;

/**
 * AI模型配置类
 * 管理不同AI模型的参数配置
 * 
 * <AUTHOR>
 */
@Slf4j
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ModelConfig {
    
    /**
     * 模型名称
     */
    private String modelName;
    
    /**
     * 最大token数
     */
    private int maxTokens;
    
    /**
     * 每个token平均字符数
     */
    private int charsPerToken;
    
    /**
     * 预留给指令和格式的token数
     */
    private int reservedTokens;
    
    /**
     * 是否支持函数调用
     */
    private boolean supportsFunctionCalling;
    
    /**
     * 是否支持长上下文
     */
    private boolean supportsLongContext;
    
    // 预定义的模型配置
    private static final Map<String, ModelConfig> PREDEFINED_CONFIGS = new HashMap<>();
    
    static {

        PREDEFINED_CONFIGS.put("deepseek", ModelConfig.builder()
                .modelName("deepseek")
                .maxTokens(64000)
                .charsPerToken(3)
                .reservedTokens(1200)
                .supportsFunctionCalling(false)
                .supportsLongContext(false)
                .build());
    }

    /**
     * 根据模型名称获取配置
     */
    public static ModelConfig getConfigByModelName(String modelName) {
        if (modelName == null || modelName.trim().isEmpty()) {
            log.warn("模型名称为空，使用默认配置");
            return getDefaultConfig();
        }

        // 精确匹配
        ModelConfig config = PREDEFINED_CONFIGS.get(modelName.toLowerCase());
        if (config != null) {
            log.info("找到模型 {} 的预定义配置", modelName);
            return config;
        }

        // 模糊匹配
        for (String key : PREDEFINED_CONFIGS.keySet()) {
            if (modelName.toLowerCase().contains(key) || key.contains(modelName.toLowerCase())) {
                config = PREDEFINED_CONFIGS.get(key);
                log.info("通过模糊匹配为模型 {} 找到配置: {}", modelName, key);
                return config;
            }
        }

        log.warn("未找到模型 {} 的配置，使用默认配置", modelName);
        return getDefaultConfig();
    }

    /**
     * 获取默认配置
     */
    public static ModelConfig getDefaultConfig() {
        return ModelConfig.builder()
                .modelName("default")
                .maxTokens(32000)
                .charsPerToken(4)
                .reservedTokens(1000)
                .supportsFunctionCalling(false)
                .supportsLongContext(false)
                .build();
    }
    
    /**
     * 添加自定义模型配置
     */
    public static void addCustomConfig(String modelName, ModelConfig config) {
        if (modelName != null && config != null) {
            PREDEFINED_CONFIGS.put(modelName.toLowerCase(), config);
            log.info("添加自定义模型配置: {}", modelName);
        }
    }
    
    /**
     * 获取可用的内容token数（扣除预留token）
     */
    public int getAvailableTokens() {
        return Math.max(0, maxTokens - reservedTokens);
    }
    
    /**
     * 获取可用的字符数
     */
    public int getAvailableChars() {
        return getAvailableTokens() * charsPerToken;
    }
    
    /**
     * 计算文本需要的token数
     */
    public int calculateTokens(String text) {
        if (text == null || text.isEmpty()) {
            return 0;
        }
        return (int) Math.ceil((double) text.length() / charsPerToken);
    }
    
    /**
     * 检查文本是否超过token限制
     */
    public boolean exceedsLimit(String text) {
        return calculateTokens(text) > getAvailableTokens();
    }
    
    /**
     * 打印配置信息
     */
    public void printConfig() {
        log.info("=== 模型配置信息 ===");
        log.info("模型名称: {}", modelName);
        log.info("最大token数: {}", maxTokens);
        log.info("每token字符数: {}", charsPerToken);
        log.info("预留token数: {}", reservedTokens);
        log.info("可用token数: {}", getAvailableTokens());
        log.info("可用字符数: {}", getAvailableChars());
        log.info("支持函数调用: {}", supportsFunctionCalling);
        log.info("支持长上下文: {}", supportsLongContext);
        log.info("==================");
    }
}
