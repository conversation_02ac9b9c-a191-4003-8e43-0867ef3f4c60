description = 'code Graph Java Analyzer - Java代码分析器'

dependencies {
    // 依赖核心模块
    api project(':code-graph-base')
    api project(':code-graph-repository')
    api project(':code-graph-ai')
    api project(':code-graph-rule')

    // Spoon for Java code analysis
    api "fr.inria.gforge.spoon:spoon-core:${spoonVersion}"
    api 'fr.inria.gforge.spoon:spoon-decompiler:0.1.0'

    // Spring Framework for dependency injection analysis
    implementation "org.springframework:spring-context:${springVersion}"
    implementation 'org.springframework:spring-core:6.1.3'
    implementation 'org.springframework:spring-beans:6.1.2'

    // Jakarta EE API
    implementation 'jakarta.annotation:jakarta.annotation-api:2.1.1'

    // Lombok
    compileOnly "org.projectlombok:lombok:${lombokVersion}"
    annotationProcessor "org.projectlombok:lombok:${lombokVersion}"

    // Logging
    implementation "ch.qos.logback:logback-classic:${logbackVersion}"

    // Test dependencies
    testImplementation platform("org.junit:junit-bom:${junitVersion}")
    testImplementation 'org.junit.jupiter:junit-jupiter'
    testImplementation "org.mockito:mockito-core:${mockitoVersion}"
}
