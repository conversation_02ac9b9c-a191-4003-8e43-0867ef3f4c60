package com.puti.code.analyzer.java.context;

import com.puti.code.ai.vector.OpenAIVectorGenerator;
import com.puti.code.base.enums.ParseType;
import com.puti.code.repository.milvus.MilvusClient;
import com.puti.code.repository.nebula.NebulaGraphClient;
import lombok.Builder;
import lombok.Getter;

@Getter
@Builder
public class GraphContext {
    private NebulaGraphClient nebulaGraphClient;
    private MilvusClient milvusClient;
    private OpenAIVectorGenerator openAIVectorGenerator;
    private ParseType parseType;
}
