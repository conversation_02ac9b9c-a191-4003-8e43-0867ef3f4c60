# 规则配置文件
# 支持Spring EL表达式语法

# 入口点规则配置
entryPointRules:
  enabled: true
  rules:
    # Spring Web MVC 注解
    - "hasAnnotation('org.springframework.web.bind.annotation.RequestMapping')"
    - "hasAnnotation('org.springframework.web.bind.annotation.GetMapping')"
    - "hasAnnotation('org.springframework.web.bind.annotation.PostMapping')"
    - "hasAnnotation('org.springframework.web.bind.annotation.PutMapping')"
    - "hasAnnotation('org.springframework.web.bind.annotation.DeleteMapping')"
    - "hasAnnotation('org.springframework.web.bind.annotation.PatchMapping')"
    
    # Spring WebFlux 注解
    - "hasAnnotation('org.springframework.web.bind.annotation.RestController') and isPublic()"
    
    # JAX-RS 注解
    - "hasAnnotation('javax.ws.rs.GET')"
    - "hasAnnotation('javax.ws.rs.POST')"
    - "hasAnnotation('javax.ws.rs.PUT')"
    - "hasAnnotation('javax.ws.rs.DELETE')"
    - "hasAnnotation('javax.ws.rs.PATCH')"
    - "hasAnnotation('javax.ws.rs.Path')"
    
    # Servlet 接口实现
    - "implementsInterface('javax.servlet.http.HttpServlet') and (methodNameEquals('doGet') or methodNameEquals('doPost') or methodNameEquals('doPut') or methodNameEquals('doDelete'))"
    
    # Spring MVC Controller 接口
    - "implementsInterface('org.springframework.web.servlet.mvc.Controller') and methodNameEquals('handleRequest')"
    
    # 定时任务
    - "hasAnnotation('org.springframework.scheduling.annotation.Scheduled')"
    
    # 事件监听器
    - "hasAnnotation('org.springframework.context.event.EventListener')"
    - "hasAnnotation('org.springframework.transaction.event.TransactionalEventListener')"
    
    # 消息队列监听器
    - "hasAnnotation('org.springframework.jms.annotation.JmsListener')"
    - "hasAnnotation('org.springframework.kafka.annotation.KafkaListener')"
    - "hasAnnotation('org.springframework.amqp.rabbit.annotation.RabbitListener')"
    
  # 自定义函数（可选）
  customFunctions:
    # 示例：检查是否为REST API
    isRestApi: "hasAnyAnnotation({'org.springframework.web.bind.annotation.GetMapping', 'org.springframework.web.bind.annotation.PostMapping', 'org.springframework.web.bind.annotation.PutMapping', 'org.springframework.web.bind.annotation.DeleteMapping'})"

# 依赖注入规则配置（预留扩展）
dependencyInjectionRules:
  enabled: false
  rules: []

# RPC规则配置（预留扩展）  
rpcRules:
  enabled: false
  rules: []
