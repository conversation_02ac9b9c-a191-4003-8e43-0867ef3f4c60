description = 'code Graph Core - 核心公共组件'

dependencies {
    // Lombok for reducing boilerplate code
    compileOnly "org.projectlombok:lombok:${lombokVersion}"
    annotationProcessor "org.projectlombok:lombok:${lombokVersion}"
    
    // SLF4J for logging
    api "org.slf4j:slf4j-api:${slf4jVersion}"
    
    // Apache Commons for utility functions
    api "org.apache.commons:commons-lang3:${commonsLang3Version}"
    api "commons-io:commons-io:${commonsIoVersion}"
    
    // Jackson for JSON processing
    api "com.fasterxml.jackson.core:jackson-databind:${jacksonVersion}"

    // gson
    api "com.google.code.gson:gson:2.10.1"

    //codec
    api "commons-codec:commons-codec:1.16.0"
    
    // Test dependencies
    testImplementation platform("org.junit:junit-bom:${junitVersion}")
    testImplementation 'org.junit.jupiter:junit-jupiter'
    testImplementation "org.mockito:mockito-core:${mockitoVersion}"

}
