package com.puti.code.base.model;

import lombok.Builder;
import lombok.Data;

/**
 * 边类
 */
@Data
@Builder
public class Edge {
    /**
     * 源节点ID
     */
    private String srcId;
    
    /**
     * 目标节点ID
     */
    private String dstId;
    
    /**
     * 边类型
     */
    private EdgeType type;
    
    /**
     * 依赖类型，仅当边类型为DEPENDS_ON时有效
     */
    private DependencyType dependencyType;
    
    /**
     * 代码行号
     */
    private Integer lineNumber;
}
