package com.puti.code.base.model;

import lombok.Getter;

/**
 * 边类型枚举
 */
@Getter
public enum EdgeType {
    CALLS("calls"),                    // 普通方法调用
    OUT_CALLS("out_calls"),           // 外部调用
    CONTAINS("contains"),             // 包含关系
    DEPENDS_ON("depends_on"),         // 依赖关系
    INSTANCE_OF("instance_of"),       // 实例化关系
    DOCUMENTED_BY("documented_by"),   // 文档关系
    IMPLEMENTED_BY("implemented_by"), //接口实现
    OVERRIDE("overridden_by"),        //方法重写
    SUPER_CALLS("super_calls"),       // 父类方法调用
    INTERFACE_CALLS("interface_calls"), // 接口方法调用
    SUBTYPE_CALLS("subtype_calls"),   // 子类方法调用
    INJECTION_CALLS("injection_calls"); // 依赖注入调用

    private final String value;

    EdgeType(String value) {
        this.value = value;
    }
}
