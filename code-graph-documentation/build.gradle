plugins {
    id 'org.springframework.boot'
    id 'io.spring.dependency-management'
}

description = 'code Graph Documentation - 说明书生成器'

dependencies {
    // 依赖核心模块
    api project(':code-graph-base')
    api project(':code-graph-ai')
    api project(':code-graph-repository')

    // Spring Boot
    implementation 'org.springframework.boot:spring-boot-starter:3.2.0'
    implementation 'org.springframework.boot:spring-boot-starter-web:3.2.0'
    implementation 'org.springframework.boot:spring-boot-starter-jdbc:3.2.0'
    implementation 'org.springframework.boot:spring-boot-starter-validation:3.2.0'

    // MyBatis
    implementation 'org.mybatis.spring.boot:mybatis-spring-boot-starter:3.0.3'

    // Database
    implementation 'mysql:mysql-connector-java:8.0.33'

    // Lombok
    compileOnly "org.projectlombok:lombok:${lombokVersion}"
    annotationProcessor "org.projectlombok:lombok:${lombokVersion}"

    // Test dependencies
    testImplementation 'org.springframework.boot:spring-boot-starter-test:3.2.0'
    testImplementation platform("org.junit:junit-bom:${junitVersion}")
    testImplementation 'org.junit.jupiter:junit-jupiter'
    testImplementation "org.mockito:mockito-core:${mockitoVersion}"
}
