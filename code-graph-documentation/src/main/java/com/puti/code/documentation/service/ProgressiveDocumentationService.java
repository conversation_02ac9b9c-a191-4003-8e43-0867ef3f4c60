package com.puti.code.documentation.service;

import com.puti.code.ai.documentation.AIDocumentationService;
import com.puti.code.ai.documentation.DocumentationGenerationContext;
import com.puti.code.base.entity.Documentation;
import com.puti.code.base.entity.DocumentationMethod;
import com.puti.code.base.entity.DocumentationTask;
import com.puti.code.base.model.MethodInfo;
import com.puti.code.base.model.SubgraphData;
import com.puti.code.documentation.config.DocumentationConfig;
import com.puti.code.documentation.dto.DocumentationGenerationDto;
import com.puti.code.documentation.repository.graph.SubgraphRepository;
import com.puti.code.documentation.repository.sql.DocumentationRepository;
import com.puti.code.documentation.repository.sql.DocumentationMethodRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * 渐进式文档生成服务
 * 负责管理分层次的文档生成流程
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class ProgressiveDocumentationService {

    private final DocumentationConfig config;
    private final SubgraphRepository subgraphRepository;
    private final AIDocumentationService aiDocumentationService;
    private final DocumentationArchiveService archiveService;
    private final DocumentationTaskService taskService;
    private final DocumentationRepository documentationRepository;
    private final DocumentationMethodRepository documentationMethodRepository;

    @Autowired
    public ProgressiveDocumentationService(DocumentationConfig config,
                                         SubgraphRepository subgraphRepository,
                                         DocumentationArchiveService archiveService,
                                         DocumentationTaskService taskService,
                                         DocumentationRepository documentationRepository,
                                         DocumentationMethodRepository documentationMethodRepository) {
        this.config = config;
        this.subgraphRepository = subgraphRepository;
        this.aiDocumentationService = AIDocumentationService.getInstance();
        this.archiveService = archiveService;
        this.taskService = taskService;
        this.documentationRepository = documentationRepository;
        this.documentationMethodRepository = documentationMethodRepository;
    }
    
    /**
     * 启动渐进式文档生成
     *
     * @param dto 请求体
     * @param entryPoint 入口点信息
     * @return 任务ID
     */
    @Async("codeDocumentTaskExecutor")
    public CompletableFuture<Long> startProgressiveGeneration(DocumentationGenerationDto dto, MethodInfo entryPoint) {
        try {
            Integer level = dto.getLevel();
            String methodId = entryPoint.getMethodId();
            log.info("开始为入口点 {} 生成 {} 层级的渐进式文档", methodId, level);

            // 1. 创建生成任务
            DocumentationTask task = taskService.createTask(dto, entryPoint);

            // 2. 异步执行渐进式生成
            executeProgressiveGeneration(task);

            return CompletableFuture.completedFuture(task.getId());

        } catch (Exception e) {
            log.error("启动渐进式文档生成失败", e);
            return CompletableFuture.failedFuture(new RuntimeException("启动生成任务失败", e));
        }
    }
    
    /**
     * 执行渐进式生成流程
     */
    private void executeProgressiveGeneration(DocumentationTask task) {
        CompletableFuture.runAsync(() -> {
            try {
                taskService.updateTaskStatus(task.getId(), DocumentationTask.TaskStatus.RUNNING);
                
                Documentation previousDoc = null;
                
                // 逐层生成文档
                for (int level = 1; level <= task.getTargetLevel(); level++) {
                    log.info("开始生成第 {} 层文档，入口点: {}", level, task.getEntryPointId());
                    
                    // 更新任务进度
                    int progress = (level - 1) * 100 / task.getTargetLevel();
                    taskService.updateTaskProgress(task.getId(), level, progress);
                    
                    // 生成当前层级的文档
                    DocumentationWithSubgraph docWithSubgraph = generateDocumentationForLevel(
                            task, level, previousDoc);

                    if (docWithSubgraph == null || docWithSubgraph.documentation == null) {
                        throw new RuntimeException("第 " + level + " 层文档生成失败");
                    }

                    Documentation currentDoc = docWithSubgraph.documentation;
                    SubgraphData subgraph = docWithSubgraph.subgraph;

                    // 保存中间态文档
                    saveIntermediateDocumentation(currentDoc, subgraph, level < task.getTargetLevel());
                    
                    previousDoc = currentDoc;
                    
                    log.info("第 {} 层文档生成完成，文档ID: {}", level, currentDoc.getId());
                }
                
                // 标记最终版本
                if (previousDoc != null) {
                    markAsFinalVersion(previousDoc);
                    
                    // 异步归档中间态版本
                    if (config.isAutoArchiveEnabled() && task.getTargetLevel() > 1) {
                        archiveService.archiveIntermediateVersions(task.getEntryPointId(), previousDoc);
                    }
                }
                
                // 完成任务
                taskService.completeTask(task.getId());
                log.info("渐进式文档生成完成，入口点: {}, 最终层级: {}", 
                        task.getEntryPointId(), task.getTargetLevel());
                
            } catch (Exception e) {
                log.error("渐进式文档生成失败，任务ID: {}", task.getId(), e);
                taskService.failTask(task.getId(), e.getMessage());
            }
        });
    }
    
    /**
     * 内部类：文档和子图数据的包装
     */
    private static class DocumentationWithSubgraph {
        final Documentation documentation;
        final SubgraphData subgraph;

        DocumentationWithSubgraph(Documentation documentation, SubgraphData subgraph) {
            this.documentation = documentation;
            this.subgraph = subgraph;
        }
    }

    /**
     * 为指定层级生成文档
     */
    private DocumentationWithSubgraph generateDocumentationForLevel(DocumentationTask task, int level,
                                                                   Documentation previousDoc) {
        try {
            String entryPointId = task.getEntryPointId();
            // 1. 获取当前层级的子图数据
            int maxSteps = config.getMaxStepsForLevel(level);
            SubgraphData subgraph = subgraphRepository.getSubgraphByLevel(entryPointId, maxSteps);
            
            if (subgraph.isEmpty()) {
                log.warn("入口点 {} 的第 {} 层子图为空", entryPointId, level);
                return null;
            }
            
            // 2. 构建生成上下文
            DocumentationGenerationContext context = DocumentationGenerationContext.builder()
                    .entryPointId(entryPointId)
                    .level(level)
                    .subgraph(subgraph)
                    .previousDocumentation(previousDoc)
                    .maxLength(config.getMaxLengthForLevel(level))
                    .build();
            
            // 3. 调用AI生成文档
            String content = aiDocumentationService.generateDocumentationForLevel(context);
            
            if (content == null || content.trim().isEmpty()) {
                log.error("AI生成的第 {} 层文档内容为空", level);
                return null;
            }
            
            // 4. 创建文档对象
            Documentation documentation = Documentation.builder()
                    .entryPointId(entryPointId)
                    .entryPointName(subgraph.getEntryPoint().getFullName())
                    .title(generateTitle(subgraph.getEntryPoint().getMethodName(), level))
                    .content(content)
                    .level(level)
                    .status(Documentation.DocumentationStatus.COMPLETED)
                    .version(1)
                    .isFinalVersion(false) // 初始都是中间态
                    .parentDocumentationId(previousDoc != null ? previousDoc.getId() : null)
                    .projectId(task.getProjectId())
                    .branchName(task.getBranchName())
                    .createdAt(LocalDateTime.now())
                    .updatedAt(LocalDateTime.now())
                    .build();

            return new DocumentationWithSubgraph(documentation, subgraph);

        } catch (Exception e) {
            log.error("生成第 {} 层文档时发生错误", level, e);
            return null;
        }
    }
    
    /**
     * 保存中间态文档
     */
    private void saveIntermediateDocumentation(Documentation documentation, SubgraphData subgraph, boolean isIntermediate) {
        try {
            documentation.setIsFinalVersion(!isIntermediate);

            // 保存文档到数据库
            Documentation savedDoc = documentationRepository.save(documentation);
            if (savedDoc == null) {
                throw new RuntimeException("保存文档失败");
            }

            // 更新文档ID（如果是新创建的）
            if (documentation.getId() == null) {
                documentation.setId(savedDoc.getId());
            }

            // 同时保存方法信息
            saveDocumentationMethods(documentation, subgraph);

            log.debug("保存文档成功，ID: {}, 层级: {}, 是否中间态: {}",
                    documentation.getId(), documentation.getLevel(), isIntermediate);

        } catch (Exception e) {
            log.error("保存中间态文档失败", e);
            throw new RuntimeException("保存文档失败", e);
        }
    }
    
    /**
     * 标记为最终版本
     */
    private void markAsFinalVersion(Documentation documentation) {
        try {
            documentation.setIsFinalVersion(true);
            documentation.setUpdatedAt(LocalDateTime.now());

            // 更新文档到数据库
            Documentation updatedDoc = documentationRepository.update(documentation);
            if (updatedDoc == null) {
                throw new RuntimeException("更新文档失败");
            }

            log.info("文档已标记为最终版本，ID: {}", documentation.getId());

        } catch (Exception e) {
            log.error("标记最终版本失败，文档ID: {}", documentation.getId(), e);
            throw new RuntimeException("标记最终版本失败", e);
        }
    }
    
    /**
     * 保存文档关联的方法信息
     */
    private void saveDocumentationMethods(Documentation documentation, SubgraphData subgraph) {
        try {
            if (documentation.getId() == null) {
                log.warn("文档ID为空，无法保存方法信息");
                return;
            }

            if (subgraph == null || CollectionUtils.isEmpty(subgraph.getMethods())) {
                log.warn("子图数据为空，无方法信息需要保存");
                return;
            }

            // 将MethodInfo转换为DocumentationMethod实体列表
            List<DocumentationMethod> documentationMethods = convertToDocumentationMethods(
                    documentation.getId(), subgraph.getMethods());

            if (!documentationMethods.isEmpty()) {
                // 批量保存方法信息
                List<DocumentationMethod> savedMethods = documentationMethodRepository.saveAll(documentationMethods);
                log.info("成功保存文档方法信息，文档ID: {}, 方法数量: {}",
                        documentation.getId(), savedMethods.size());
            }

        } catch (Exception e) {
            log.error("保存文档方法信息失败，文档ID: {}", documentation.getId(), e);
            // 不抛出异常，避免影响主流程
        }
    }

    /**
     * 将MethodInfo列表转换为DocumentationMethod实体列表
     */
    private List<DocumentationMethod> convertToDocumentationMethods(Long documentationId, List<MethodInfo> methods) {
        List<DocumentationMethod> documentationMethods = new ArrayList<>();

        for (MethodInfo methodInfo : methods) {
            try {
                DocumentationMethod docMethod = DocumentationMethod.builder()
                        .documentationId(documentationId)
                        .methodId(methodInfo.getMethodId())
                        .methodName(methodInfo.getFullName())
                        .methodType(determineMethodType(methodInfo))
                        .callLevel(methodInfo.getLevel())
                        .description(methodInfo.getDescription())
                        .signature(methodInfo.getSignature())
                        .className(methodInfo.getClassName())
                        .createdAt(LocalDateTime.now())
                        .build();

                documentationMethods.add(docMethod);

            } catch (Exception e) {
                log.warn("转换方法信息失败，跳过该方法: {}", methodInfo.getMethodId(), e);
            }
        }

        return documentationMethods;
    }

    /**
     * 根据MethodInfo确定方法类型
     */
    private DocumentationMethod.MethodType determineMethodType(MethodInfo methodInfo) {
        // 根据方法信息判断方法类型
        if (Boolean.TRUE.equals(methodInfo.getIsEntryPoint())) {
            return DocumentationMethod.MethodType.ENTRY_POINT;
        }

        // 根据调用层级判断
        Integer level = methodInfo.getLevel();
        if (level != null) {
            if (level == 1) {
                return DocumentationMethod.MethodType.DIRECT_CALL;
            } else if (level > 1) {
                return DocumentationMethod.MethodType.INDIRECT_CALL;
            }
        }

        // 根据方法名判断是否为工具方法
        String methodName = methodInfo.getMethodName();
        if (methodName != null && (methodName.startsWith("get") || methodName.startsWith("set") ||
                                  methodName.startsWith("is") || methodName.contains("Util") ||
                                  methodName.contains("Helper"))) {
            return DocumentationMethod.MethodType.UTILITY_METHOD;
        }

        // 默认为间接调用
        return DocumentationMethod.MethodType.INDIRECT_CALL;
    }

    /**
     * 生成文档标题
     */
    private String generateTitle(String methodName, int level) {
        String levelDesc = switch (level) {
            case 1 -> "核心流程";
            case 2 -> "详细流程";
            case 3 -> "完整文档";
            default -> "文档";
        };
        
        return String.format("%s - %s说明书", methodName, levelDesc);
    }
}
