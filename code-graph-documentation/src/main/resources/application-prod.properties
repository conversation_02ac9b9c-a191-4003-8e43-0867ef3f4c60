# Production Environment Configuration

# Database Configuration - Production MySQL
spring.datasource.url=${DB_URL:**************************************************************************************************************************************************************}
spring.datasource.username=${DB_USERNAME}
spring.datasource.password=${DB_PASSWORD}

# HikariCP Connection Pool Configuration - Production (Larger Pool)
spring.datasource.hikari.pool-name=DocumentationProdHikariCP
spring.datasource.hikari.minimum-idle=10
spring.datasource.hikari.maximum-pool-size=50
spring.datasource.hikari.leak-detection-threshold=60000

# MyBatis Configuration - Production (Use SLF4J)
mybatis.configuration.log-impl=org.apache.ibatis.logging.slf4j.Slf4jImpl

# Logging Configuration - Production (Less Verbose)
logging.level.com.puti.code.documentation=INFO
logging.level.com.puti.code.documentation.mapper=INFO
logging.file.name=code-graph-documentation
logging.file.path=../logs

# Server Configuration - Production (Performance Optimized)
server.port=${SERVER_PORT:8080}
server.shutdown=graceful
server.tomcat.max-connections=8192
server.tomcat.threads.max=200
server.tomcat.threads.min-spare=10

# Documentation Configuration - Production (Optimized Settings)
documentation.ai.max_retry=5
documentation.ai.retry_delay_ms=2000
documentation.archive.delay_days=3
documentation.archive.max_intermediate_versions=3
documentation.concurrent.max_tasks=10
documentation.concurrent.timeout_minutes=60
documentation.storage.path=/data/documentation
