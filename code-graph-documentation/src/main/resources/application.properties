# Spring Boot Application Configuration
spring.application.name=code-graph-documentation

# Database Configuration - MySQL (Common Settings)
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
spring.datasource.hikari.connection-timeout=30000
spring.datasource.hikari.idle-timeout=600000
spring.datasource.hikari.max-lifetime=1800000
spring.datasource.hikari.connection-test-query=SELECT 1

# MyBatis Configuration
mybatis.type-aliases-package=com.puti.code.documentation.entity
mybatis.mapper-locations=classpath:mapper/*.xml
mybatis.configuration.map-underscore-to-camel-case=true
mybatis.configuration.lazy-loading-enabled=true
mybatis.configuration.cache-enabled=true

# Server Configuration
server.port=8080
server.servlet.context-path=/documentation

# Documentation Generation Configuration (Common Settings)
documentation.level1.max_steps=2
documentation.level1.max_length=1000
documentation.level2.max_steps=5
documentation.level2.max_length=3000
documentation.level3.max_steps=10
documentation.level3.max_length=10000
documentation.ai.max_tokens=4000
documentation.archive.auto_enabled=true
documentation.storage.use_database=true
documentation.storage.use_filesystem=false
